# agent/tools.py
import os
import subprocess
from crewai_tools import BaseTool

class FileReadTool(BaseTool):
    name: str = "File Read Tool"
    description: str = "Reads the entire content of a specified file. Use this to understand existing code before making changes."

    def _run(self, filename: str) -> str:
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            return f"Error reading file {filename}: {e}"

class FileWriteTool(BaseTool):
    name: str = "File Write Tool"
    description: str = "Writes content to a specified file. This will create the file if it does not exist or overwrite it if it does."

    def _run(self, filename: str, content: str) -> str:
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            return f"File {filename} has been written to successfully."
        except Exception as e:
            return f"Error writing to file {filename}: {e}"

class ListFilesTool(BaseTool):
    name: str = "List Files Tool"
    description: str = "Lists all files and directories in a given path to understand the project structure."

    def _run(self, path: str = '.') -> str:
        try:
            return "\n".join(os.listdir(path))
        except Exception as e:
            return f"Error listing files in {path}: {e}"

class ShellCommandTool(BaseTool):
    name: str = "Shell Command Tool"
    description: str = "Executes a shell command. Use this for tasks like running tests, installing dependencies, or running linters."

    def _run(self, command: str) -> str:
        try:
            result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True, timeout=60)
            output = f"STDOUT:\n{result.stdout}\n"
            if result.stderr:
                output += f"STDERR:\n{result.stderr}"
            return output
        except subprocess.CalledProcessError as e:
            return f"Command '{command}' failed with exit code {e.returncode}.\nSTDOUT:\n{e.stdout}\nSTDERR:\n{e.stderr}"
        except subprocess.TimeoutExpired as e:
            return f"Command '{command}' timed out after 60 seconds."

# Instantiate your tools for easy import
file_read_tool = FileReadTool()
file_write_tool = FileWriteTool()
list_files_tool = ListFilesTool()
shell_command_tool = ShellCommandTool()
