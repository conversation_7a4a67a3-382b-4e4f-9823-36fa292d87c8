# tests/test_tools.py
import unittest
import os
import shutil
from agent.tools import FileReadTool, FileWriteTool, ListFilesTool, ShellCommandTool

class TestAgentTools(unittest.TestCase):

    def setUp(self):
        """Set up a test environment before each test."""
        self.test_dir = "test_workspace_for_unittest"
        os.makedirs(self.test_dir, exist_ok=True)
        self.test_file = os.path.join(self.test_dir, "test_file.txt")
        self.test_content = "Hello, world!"
        with open(self.test_file, "w", encoding='utf-8') as f:
            f.write(self.test_content)

    def tearDown(self):
        """Clean up the test environment after each test."""
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)

    def test_file_read_tool(self):
        """Test the FileReadTool."""
        tool = FileReadTool()
        result = tool.run(filename=self.test_file)
        self.assertEqual(result, self.test_content)

    def test_file_write_tool(self):
        """Test the FileWriteTool."""
        tool = FileWriteTool()
        new_content = "This is new content."
        new_file = os.path.join(self.test_dir, "new_file.txt")
        tool.run(filename=new_file, content=new_content)
        self.assertTrue(os.path.exists(new_file))
        with open(new_file, "r", encoding='utf-8') as f:
            self.assertEqual(f.read(), new_content)

    def test_list_files_tool(self):
        """Test the ListFilesTool."""
        tool = ListFilesTool()
        result = tool.run(path=self.test_dir)
        self.assertIn("test_file.txt", result)

    def test_shell_command_tool(self):
        """Test the ShellCommandTool with a safe command."""
        tool = ShellCommandTool()
        command = "echo 'hello from shell'"
        result = tool.run(command=command)
        self.assertIn("hello from shell", result)

if __name__ == "__main__":
    unittest.main()
