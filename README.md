AI Coding Agent: Project Genesis
This project is a sophisticated AI coding agent designed to act as an expert pair programmer. It uses a powerful Large Language Model (LLM) as its "brain" and a set of custom tools as its "body" to understand and execute complex coding tasks autonomously.

The agent operates based on the expert constitution defined in agent/prompts.py.

Step-by-Step Guide to Using This Project
Part 1: Project Setup
Create the Folder Structure: Create all the folders and files as laid out in the project structure above.

Copy the Code: Copy and paste the code from each section of the guide into the corresponding files.

Create a Virtual Environment: Open your terminal in the project root (/ai-coding-agent/) and run the commands appropriate for your operating system.

On Windows (in PowerShell):

First, use the py launcher to create the virtual environment. This avoids issues with the standard python alias.

py -m venv venv

Next, to activate it, you may first need to allow scripts to run in your current PowerShell session. Run this command:

Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process

Now, activate the virtual environment:

.\venv\Scripts\activate

On macOS / Linux (in bash or zsh):

python3 -m venv venv
source venv/bin/activate

Install Dependencies:

pip install -r requirements.txt

Part 2: Configuration
Set Up Environment Variables:

In the project root, create a file named .env.

Copy the contents of the .env template into this new file.

Crucially, get your OpenRouter API key by signing up at openrouter.ai. It's free.

Paste your key into the .env file.

Choose Your Model on OpenRouter:

Edit the .env file and set OPENAI_MODEL_NAME to the free model you want to use.

Recommended starting model: moonshotai/kimi-k2:free

Other great options: deepseek/deepseek-r1:free, google/gemini-2.0-flash-experimental:free

Part 3: Running the Agent
Start the Agent: Run the main.py script from your terminal:

python agent/main.py

Give it a Task: The script will prompt you to enter a task. Be descriptive! For example:

"Read the README.md file and tell me what the first setup instruction is."

Watch it Work: The agent will now use its tools and reasoning to complete the task. The output will be verbose so you can see its thought process.

Part 4: Advanced - Fine-Tuning Your Own Model
If you find the agent isn't behaving exactly as you want, you can fine-tune a base model.

Generate a Larger Dataset:

You'll need an API key from a provider like OpenAI or Anthropic for this. Add it to your .env file.

Run the data generation script (you'll need to create this, a template is in the previous guide) to create a large dataset.jsonl file with thousands of examples.

Train in the Cloud (Google Colab):

Sign up for Google Colab Pro (highly recommended).

Create a new notebook.

Upload your training/train.py script and your data/dataset.jsonl file.

In a notebook cell, run the command: !python train.py

This will use Google's powerful GPUs to train your model. It may take a few hours.

Use Your Trained Model:

Once training is complete, download the fine_tuned_adapter folder from Colab.

Place this folder inside your local training/ directory.

To use it, you would need to modify the loading logic to merge the base model with your adapter. This is an advanced step that requires using the PeftModel class from Hugging Face's PEFT library.