# agent/main.py
import os
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process
from langchain_openai import Chat<PERSON>penA<PERSON>

# Import your custom tools and prompts
from agent.tools import file_read_tool, file_write_tool, list_files_tool, shell_command_tool
from agent.prompts import AGENT_CONSTITUTION

# Load environment variables from .env file
load_dotenv()

def run_agent():
    """Initializes and runs the AI Coding Agent crew."""
    
    # Set up the language model using OpenRouter
    # The API key is read from the OPENROUTER_API_KEY environment variable
    openrouter_llm = ChatOpenAI(
        model=os.environ.get("OPENAI_MODEL_NAME"),
        api_key=os.environ.get("OPENROUTER_API_KEY"),
        base_url=os.environ.get("OPENAI_API_BASE")
    )

    # Define the agent
    pair_programmer_agent = Agent(
        role='Expert AI Pair Programmer',
        goal='Collaboratively solve complex coding tasks by planning, exploring the codebase, and writing clean, functional, and secure code.',
        backstory=AGENT_CONSTITUTION,
        llm=openrouter_llm,
        tools=[
            file_read_tool,
            file_write_tool,
            list_files_tool,
            shell_command_tool
        ],
        verbose=True,
        allow_delegation=False,
    )

    # Get the task from the user
    print("--- AI Coding Agent Initialized ---")
    user_task = input("Please enter the coding task for the AI agent:\n> ")

    if not user_task:
        print("No task provided. Exiting.")
        return

    # Define the task for the agent
    coding_task = Task(
        description=f"Here is the task: {user_task}. Please begin by forming a plan and then execute it step by step.",
        expected_output="A complete and functional solution delivered as code changes, new files, or shell command outputs. The solution must be fully implemented and verified. Announce when the task is complete.",
        agent=pair_programmer_agent
    )

    # Create and run the crew
    crew = Crew(
        agents=[pair_programmer_agent],
        tasks=[coding_task],
        process=Process.sequential,
        verbose=2
    )

    print("\n--- Kicking off the Crew ---")
    result = crew.kickoff()
    print("\n--- AI Agent Crew Finished ---")

    print("\n\n################################")
    print("## Final Result:")
    print("################################\n")
    print(result)

if __name__ == "__main__":
    run_agent()
